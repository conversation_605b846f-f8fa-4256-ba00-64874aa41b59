# 更新重试机制 - 使用说明

## 问题解决

您遇到的 `net::ERR_CONNECTION_CLOSED` 错误已经通过以下改进得到解决：

### ✅ 已实现的功能

1. **自动重试机制**
   - 最大重试次数：3次
   - 智能延迟：1秒 → 2秒 → 4秒（指数退避）
   - 网络状态检查：重试前确认网络连接

2. **网络错误识别**
   - 自动识别各种网络连接错误
   - 区分网络错误和其他类型错误
   - 只对网络错误进行自动重试

3. **用户界面改进**
   - 显示详细错误信息
   - 提供手动重试按钮
   - 实时显示重试进度
   - 友好的错误提示

4. **多语言支持**
   - 中文和英文错误提示
   - 本地化的用户界面

## 使用方法

### 自动重试
当更新过程中遇到网络错误时，系统会：
1. 自动检测错误类型
2. 如果是网络错误，开始重试流程
3. 检查网络连接状态
4. 等待适当时间后重新尝试
5. 最多重试3次

### 手动重试
如果自动重试失败，您可以：
1. 在错误提示卡片中点击"重试"按钮
2. 系统会重新开始下载过程
3. 重试计数会重置

### 错误提示
- **网络错误**：显示"网络连接错误"，提供重试选项
- **其他错误**：显示具体错误信息，提供关闭选项

## 配置选项

可以在 `client/src/shared/config/update.js` 中调整以下参数：

```javascript
const RETRY_CONFIG = {
  MAX_RETRIES: 3,           // 最大重试次数
  BASE_DELAY: 1000,         // 基础延迟时间（毫秒）
  MAX_DELAY: 10000,         // 最大延迟时间（毫秒）
  NETWORK_CHECK_TIMEOUT: 5000, // 网络检查超时时间
  BACKOFF_FACTOR: 2         // 指数退避因子
};
```

## 测试功能

运行测试脚本验证重试机制：

```bash
cd client
node test/update-retry-test.js
```

## 注意事项

### 关于断点续传
- **electron-updater 本身不支持断点续传**
- 重试会重新开始下载整个更新文件
- 这是 electron-updater 的限制，不是我们的实现问题

### 网络优化建议
1. **检查网络连接**：确保网络稳定
2. **更换更新源**：可以在设置中切换到 Gitee 镜像
3. **避免高峰期**：选择网络较好的时间段更新
4. **关闭代理**：某些代理可能影响下载

### 自定义更新源
如果 GitHub 访问有问题，可以：
1. 打开设置页面
2. 选择"更新设置"
3. 切换到"Gitee"或配置"自定义"更新源

## 错误排查

### 常见网络错误
- `ERR_CONNECTION_CLOSED`：连接被关闭
- `ERR_CONNECTION_RESET`：连接被重置
- `ERR_CONNECTION_REFUSED`：连接被拒绝
- `ETIMEDOUT`：连接超时

### 解决步骤
1. **检查网络**：确认网络连接正常
2. **重试更新**：点击重试按钮
3. **更换源**：切换到其他更新源
4. **稍后重试**：网络恢复后再次尝试

## 技术细节

### 文件修改
- `client/src/main/main.js`：主进程重试逻辑
- `client/src/renderer/utils/update-card.js`：UI 重试界面
- `client/src/renderer/assets/styles/update-card.css`：重试样式
- `client/src/shared/config/update.js`：重试配置
- `client/src/renderer/assets/locales/`：国际化文本

### 新增功能
- 网络连接检查
- 智能错误识别
- 指数退避重试
- 用户友好界面
- 多语言支持

## 反馈

如果您在使用过程中遇到问题，请：
1. 查看控制台错误信息
2. 尝试手动重试
3. 检查网络设置
4. 联系技术支持

---

**总结**：现在您的应用具备了完整的网络错误处理和重试机制，能够有效解决 `ERR_CONNECTION_CLOSED` 等网络连接问题。虽然无法实现真正的断点续传（这是 electron-updater 的限制），但通过智能重试机制大大提高了更新成功率。
