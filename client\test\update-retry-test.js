/**
 * 更新重试机制测试脚本
 * 用于测试网络错误处理和重试功能
 */

const { app, BrowserWindow, ipcMain } = require('electron');
const { autoUpdater } = require('electron-updater');

// 模拟网络错误的测试函数
class UpdateRetryTester {
  constructor() {
    this.testResults = [];
    this.originalCheckForUpdates = autoUpdater.checkForUpdates.bind(autoUpdater);
    this.originalDownloadUpdate = autoUpdater.downloadUpdate.bind(autoUpdater);
  }

  /**
   * 模拟网络错误
   * @param {string} errorType - 错误类型
   */
  simulateNetworkError(errorType = 'ERR_CONNECTION_CLOSED') {
    const error = new Error(`Simulated ${errorType}`);
    error.code = errorType;
    return error;
  }

  /**
   * 测试网络错误检测
   */
  testNetworkErrorDetection() {
    console.log('🧪 测试网络错误检测...');
    
    const networkErrors = [
      'ERR_CONNECTION_CLOSED',
      'ERR_CONNECTION_RESET',
      'ERR_CONNECTION_REFUSED',
      'ERR_NETWORK_CHANGED',
      'ERR_INTERNET_DISCONNECTED',
      'ENOTFOUND',
      'ECONNRESET',
      'ETIMEDOUT'
    ];

    const nonNetworkErrors = [
      'ERR_FILE_NOT_FOUND',
      'ERR_INVALID_RESPONSE',
      'ERR_PERMISSION_DENIED'
    ];

    // 导入主进程的错误检测函数（需要从 main.js 导出）
    const { isNetworkError } = require('../src/main/main.js');

    let passed = 0;
    let total = 0;

    // 测试网络错误
    networkErrors.forEach(errorType => {
      total++;
      const error = this.simulateNetworkError(errorType);
      if (isNetworkError(error)) {
        passed++;
        console.log(`✅ ${errorType} 正确识别为网络错误`);
      } else {
        console.log(`❌ ${errorType} 未被识别为网络错误`);
      }
    });

    // 测试非网络错误
    nonNetworkErrors.forEach(errorType => {
      total++;
      const error = this.simulateNetworkError(errorType);
      if (!isNetworkError(error)) {
        passed++;
        console.log(`✅ ${errorType} 正确识别为非网络错误`);
      } else {
        console.log(`❌ ${errorType} 被错误识别为网络错误`);
      }
    });

    console.log(`\n📊 网络错误检测测试结果: ${passed}/${total} 通过`);
    return { passed, total };
  }

  /**
   * 测试重试机制
   */
  async testRetryMechanism() {
    console.log('\n🧪 测试重试机制...');
    
    let retryCount = 0;
    const maxRetries = 3;

    // 模拟重试函数
    const mockRetry = async () => {
      retryCount++;
      console.log(`🔄 第 ${retryCount} 次重试`);
      
      if (retryCount < maxRetries) {
        // 前几次重试失败
        throw this.simulateNetworkError('ERR_CONNECTION_CLOSED');
      } else {
        // 最后一次成功
        console.log('✅ 重试成功');
        return { success: true };
      }
    };

    try {
      // 模拟重试逻辑
      while (retryCount < maxRetries) {
        try {
          const result = await mockRetry();
          if (result.success) {
            console.log(`📊 重试机制测试: 在第 ${retryCount} 次重试后成功`);
            return { success: true, retryCount };
          }
        } catch (error) {
          if (retryCount >= maxRetries) {
            console.log(`❌ 达到最大重试次数 (${maxRetries})，测试失败`);
            return { success: false, retryCount };
          }
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
    } catch (error) {
      console.log(`❌ 重试机制测试失败: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试网络连接检查
   */
  async testNetworkCheck() {
    console.log('\n🧪 测试网络连接检查...');
    
    // 这里需要导入主进程的网络检查函数
    // const { checkNetworkConnection } = require('../src/main/main.js');
    
    // 模拟网络检查
    const mockNetworkCheck = async () => {
      // 模拟网络检查逻辑
      return new Promise((resolve) => {
        setTimeout(() => {
          // 随机返回网络状态
          const isConnected = Math.random() > 0.3;
          resolve(isConnected);
        }, 100);
      });
    };

    const isConnected = await mockNetworkCheck();
    console.log(`📊 网络连接状态: ${isConnected ? '已连接' : '未连接'}`);
    
    return { connected: isConnected };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行更新重试机制测试...\n');
    
    const results = {
      networkErrorDetection: null,
      retryMechanism: null,
      networkCheck: null
    };

    try {
      // 测试网络错误检测
      results.networkErrorDetection = this.testNetworkErrorDetection();
      
      // 测试重试机制
      results.retryMechanism = await this.testRetryMechanism();
      
      // 测试网络连接检查
      results.networkCheck = await this.testNetworkCheck();
      
      // 输出总结
      console.log('\n📋 测试总结:');
      console.log('=====================================');
      console.log(`网络错误检测: ${results.networkErrorDetection.passed}/${results.networkErrorDetection.total} 通过`);
      console.log(`重试机制: ${results.retryMechanism.success ? '✅ 通过' : '❌ 失败'}`);
      console.log(`网络连接检查: ${results.networkCheck.connected ? '✅ 连接正常' : '⚠️ 连接异常'}`);
      
      return results;
    } catch (error) {
      console.error('❌ 测试运行失败:', error);
      return { error: error.message };
    }
  }

  /**
   * 模拟更新错误场景
   */
  simulateUpdateError(errorType = 'ERR_CONNECTION_CLOSED') {
    console.log(`🎭 模拟更新错误: ${errorType}`);
    
    // 替换 autoUpdater 的方法来模拟错误
    autoUpdater.downloadUpdate = () => {
      return Promise.reject(this.simulateNetworkError(errorType));
    };
    
    console.log('✅ 错误模拟已设置，下次更新将触发指定错误');
  }

  /**
   * 恢复正常更新功能
   */
  restoreNormalUpdate() {
    console.log('🔄 恢复正常更新功能...');
    autoUpdater.checkForUpdates = this.originalCheckForUpdates;
    autoUpdater.downloadUpdate = this.originalDownloadUpdate;
    console.log('✅ 更新功能已恢复正常');
  }
}

// 导出测试类
module.exports = UpdateRetryTester;

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new UpdateRetryTester();
  tester.runAllTests().then(results => {
    console.log('\n🎯 测试完成，结果:', JSON.stringify(results, null, 2));
    process.exit(0);
  }).catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
}
