# 更新重试机制说明

## 概述

为了解决 `net::ERR_CONNECTION_CLOSED` 等网络连接问题导致的更新失败，我们实现了一套完整的重试机制和错误处理系统。

## 主要功能

### 1. 自动重试机制
- **最大重试次数**: 3次
- **指数退避延迟**: 1秒、2秒、4秒（最大10秒）
- **网络状态检查**: 重试前检查网络连接
- **智能错误识别**: 区分网络错误和其他错误类型

### 2. 网络错误检测
系统能够识别以下网络错误类型：
- `ERR_CONNECTION_CLOSED`
- `ERR_CONNECTION_RESET`
- `ERR_CONNECTION_REFUSED`
- `ERR_NETWORK_CHANGED`
- `ERR_INTERNET_DISCONNECTED`
- `ENOTFOUND`
- `ECONNRESET`
- `ETIMEDOUT`

### 3. 用户界面改进
- **错误详情显示**: 显示具体错误信息和重试次数
- **重试按钮**: 用户可以手动触发重试
- **重试进度**: 显示当前重试状态
- **友好提示**: 针对网络错误提供解决建议

## 技术实现

### 主进程 (main.js)

#### 新增状态管理
```javascript
let updateState = {
  available: false,
  info: null,
  downloading: false,
  progress: null,
  downloaded: false,
  retrying: false,      // 新增：是否正在重试
  retryCount: 0,        // 新增：当前重试次数
  maxRetries: 3,        // 新增：最大重试次数
  lastError: null       // 新增：最后一次错误
};
```

#### 核心功能函数
1. **网络连接检查**: `checkNetworkConnection()`
2. **错误类型判断**: `isNetworkError(error)`
3. **重试逻辑**: `retryUpdateDownload()`

#### IPC 接口
- `retry-update-download`: 手动重试下载
- `get-update-state`: 获取更新状态
- `reset-update-state`: 重置更新状态

### 渲染进程 (update-card.js)

#### 新增事件监听
- `update-retrying`: 重试进度通知
- `update-retry-failed`: 重试失败通知

#### UI 状态
- **错误状态**: 显示错误详情和重试选项
- **重试状态**: 显示重试进度
- **重试失败**: 显示最终失败信息

### CSS 样式 (update-card.css)

#### 新增样式类
- `.update-card.retrying`: 重试状态样式
- `.update-error-details`: 错误详情容器
- `.retry-spinner`: 重试加载动画

## 使用方法

### 自动重试
当检测到网络错误时，系统会自动进行重试：
1. 检查网络连接状态
2. 等待指数退避延迟
3. 重新尝试下载
4. 重复直到成功或达到最大重试次数

### 手动重试
用户可以通过以下方式手动重试：
1. 在错误提示卡片中点击"重试"按钮
2. 系统会重置重试计数并开始新的下载尝试

## 配置选项

### 重试配置
```javascript
// 在 main.js 中可以调整以下参数
updateState.maxRetries = 3;        // 最大重试次数
const delayTime = Math.min(1000 * Math.pow(2, retryCount - 1), 10000); // 延迟算法
```

### 网络检查超时
```javascript
// 网络连接检查超时时间
request.setTimeout(5000, () => {
  resolve(false);
});
```

## 错误处理流程

```
更新开始
    ↓
下载过程中发生错误
    ↓
判断错误类型
    ↓
是网络错误？
    ↓ 是
检查重试次数 < 最大重试次数？
    ↓ 是
检查网络连接
    ↓
网络可用？
    ↓ 是
等待延迟时间
    ↓
重新开始下载
    ↓
成功？
    ↓ 否
重复重试流程
    ↓
达到最大重试次数
    ↓
显示重试失败提示
```

## 国际化支持

系统支持中英文错误提示：

### 中文
- "网络连接错误"
- "重试"
- "请检查网络连接后重试"

### 英文
- "Network connection error"
- "Retry"
- "Please check your network connection and try again"

## 注意事项

1. **断点续传**: electron-updater 本身不支持断点续传，重试会重新开始下载
2. **网络检查**: 使用 GitHub 连接测试网络状态
3. **错误区分**: 只对网络相关错误进行自动重试
4. **用户体验**: 提供清晰的错误信息和操作指引

## 未来改进

1. **断点续传**: 考虑使用其他更新方案支持断点续传
2. **多源下载**: 实现多个更新源的自动切换
3. **智能重试**: 根据网络状况调整重试策略
4. **离线检测**: 更精确的网络状态检测
